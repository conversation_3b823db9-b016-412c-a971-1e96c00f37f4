'use client';

import type { FC } from 'react';
import { useTranslations } from 'next-intl';

import Image from 'next/image';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import TryForFreeModal from '@/components/TryForFreeModal';
import { useCart } from '@/contexts/CartContext';

import { useRouter } from '@/libs/i18nNavigation';
import { authService } from '@/services/auth';

// Removed unused Discord invite link

const Header: FC = () => {
  const router = useRouter();
  const t = useTranslations('Header');
  const { cartCount } = useCart();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isTryForFreeModalOpen, setIsTryForFreeModalOpen] = useState(false);

  useEffect(() => {
    const checkAuth = async () => {
      const authStatus = await authService.checkAuthStatus();
      setIsAuthenticated(authStatus);
    };

    checkAuth();
  }, []);

  // Listen for auth change events
  useEffect(() => {
    // Event listener for auth status changes
    const handleAuthChange = () => {
      setIsAuthenticated(true);
    };

    // Add event listener for auth changes
    window.addEventListener('kitsify-auth-change', handleAuthChange);

    // Clean up event listener
    return () => {
      window.removeEventListener('kitsify-auth-change', handleAuthChange);
    };
  }, []);

  const handleAuthButtonClick = () => {
    if (isAuthenticated) {
      router.push('/tools');
    } else {
      router.push('/sign-in');
    }
  };

  const handleTryForFreeClick = () => {
    setIsTryForFreeModalOpen(true);
  };

  return (
    <header className="sticky top-0 z-50 shadow-md transition-all duration-300">
      <nav className="fixed start-0 top-0 z-20 w-full border-b border-gray-200 bg-white">
        <div className="container mx-auto flex flex-wrap items-center justify-between py-2">
          <Link href="/" className="flex items-center rtl:space-x-reverse">
            <Image src="/assets/images/kitsify_rect.png" alt="Kitsify Logo" height={55} width={0} unoptimized style={{ height: '55px', width: 'auto' }} />
          </Link>
          <div className="flex items-center space-x-1 md:order-2 md:space-x-2 rtl:space-x-reverse">
            {/* Cart Icon */}
            <button
              type="button"
              onClick={() => router.push('/cart')}
              className="relative rounded-full p-2 text-gray-600 transition-colors duration-200 hover:bg-gray-100 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
              title="View Cart"
              aria-label={`Shopping cart with ${cartCount} items`}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="size-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                strokeWidth={2}
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"
                />
              </svg>
              {cartCount > 0 && (
                <span className="absolute -right-1 -top-1 flex size-5 items-center justify-center rounded-full bg-red-600 text-xs font-bold text-white shadow-sm">
                  {cartCount > 99 ? '99+' : cartCount}
                </span>
              )}
            </button>

            <button
              type="button"
              onClick={handleTryForFreeClick}
              className="rounded-xl bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-800 focus:outline-none focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
            >
              {t('try_for_free')}
            </button>
            <button
              type="button"
              onClick={handleAuthButtonClick}
              className="rounded-xl border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-4 focus:ring-gray-100"
            >
              {isAuthenticated ? t('dashboard') : t('login')}
            </button>
          </div>
          <div className="hidden w-full items-center justify-between md:order-1 md:flex md:w-auto" id="navbar-sticky">
            <ul className="mt-4 flex flex-col rounded-lg border border-gray-100 bg-gray-50 p-4 font-medium md:mt-0 md:flex-row md:space-x-8 md:border-0 md:bg-white md:p-0 rtl:space-x-reverse">
              <li>
                <a href="#top" className="block rounded-sm px-3 py-2 text-lg text-gray-900 hover:bg-gray-100 md:p-0 md:hover:bg-transparent md:hover:text-blue-700 md:dark:hover:bg-transparent md:dark:hover:text-blue-500">{t('home')}</a>
              </li>
              <li>
                <a href="#pricing" className="block rounded-sm px-3 py-2 text-lg text-gray-900 hover:bg-gray-100 md:p-0 md:hover:bg-transparent md:hover:text-blue-700 md:dark:hover:bg-transparent md:dark:hover:text-blue-500">{t('pricing')}</a>
              </li>
              <li>
                <a href="#tools" className="block rounded-sm px-3 py-2 text-lg text-gray-900 hover:bg-gray-100 md:p-0 md:hover:bg-transparent md:hover:text-blue-700 md:dark:hover:bg-transparent md:dark:hover:text-blue-500">{t('tools')}</a>
              </li>
              <li>
                <a href="#guide" className="block rounded-sm px-3 py-2 text-lg text-gray-900 hover:bg-gray-100 md:p-0 md:hover:bg-transparent md:hover:text-blue-700 md:dark:hover:bg-transparent md:dark:hover:text-blue-500">{t('guide')}</a>
              </li>
              <li>
                <Link href="/shop" className="block rounded-sm px-3 py-2 text-lg text-gray-900 hover:bg-gray-100 md:p-0 md:hover:bg-transparent md:hover:text-blue-700 md:dark:hover:bg-transparent md:dark:hover:text-blue-500">
                  {t('shop')}
                </Link>
              </li>
            </ul>
          </div>
        </div>
      </nav>

      {/* Try For Free Modal */}
      <TryForFreeModal
        isOpen={isTryForFreeModalOpen}
        onClose={() => setIsTryForFreeModalOpen(false)}
      />
    </header>
  );
};

export default Header;
