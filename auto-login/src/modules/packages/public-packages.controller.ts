import {
  Controller,
  Get,
  Query,
  Param,
  HttpException,
  HttpStatus,
  ParseIntPipe,
  NotFoundException,
} from '@nestjs/common';
import { PackagesService } from './packages.service';

@Controller('public/packages')
export class PublicPackagesController {
  constructor(private readonly packagesService: PackagesService) {}

  @Get('durations/filter')
  async getPackageDurations(
    @Query('months') months?: string,
    @Query('currency') currency: string = 'USD',
    @Query('includePaymentMethods') includePaymentMethods?: string,
  ) {
    try {
      // Convert months to days for filtering
      let days: number[] | undefined;
      if (months) {
        const monthValues = months
          .split(',')
          .map((m) => parseInt(m.trim(), 10));
        days = monthValues.map((month) => (month === 12 ? 365 : month * 30)); // Special case for 1 year (365 days)
      }

      const includePayments = includePaymentMethods === 'true';

      return await this.packagesService.findPackageDurations(
        days,
        currency,
        includePayments,
      );
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve package durations',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('durations/with-discounts')
  async getPackageDurationsWithDiscounts(
    @Query('months') months?: string,
    @Query('currency') currency: string = 'USD',
  ) {
    try {
      // Convert months to days for filtering
      let days: number[] | undefined;
      if (months) {
        const monthValues = months
          .split(',')
          .map((m) => parseInt(m.trim(), 10));
        days = monthValues.map((month) => (month === 12 ? 365 : month * 30)); // Special case for 1 year (365 days)
      }

      return await this.packagesService.findPackageDurations(
        days,
        currency,
        true, // Always include payment methods for this endpoint
      );
    } catch (error) {
      throw new HttpException(
        'Failed to retrieve package durations with discounts',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('durations/:id')
  async getPackageDuration(@Param('id', ParseIntPipe) id: number) {
    try {
      return await this.packagesService.findPackageDuration(id);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }
      throw new HttpException(
        'Failed to retrieve package duration',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('pricing/:packageId/:durationId')
  async getPackagePricingWithDiscounts(
    @Param('packageId', ParseIntPipe) packageId: number,
    @Param('durationId', ParseIntPipe) durationId: number,
    @Query('currency') currency: string = 'USD',
  ) {
    try {
      const packageDuration =
        await this.packagesService.findPackageDuration(durationId);

      if (!packageDuration || packageDuration.package_id !== packageId) {
        throw new HttpException(
          'Package duration not found',
          HttpStatus.NOT_FOUND,
        );
      }

      // Get package discounts
      const packageDiscounts =
        await this.packagesService.getPackageDiscounts(packageId);

      // Calculate pricing for each payment method
      const pricingOptions = [];

      for (const discount of packageDiscounts) {
        const originalPrice = parseFloat(packageDuration.price);
        const paymentMethodDiscountPercent = discount.discount_percent;

        // Check if package duration has its own discount
        const packageDiscountPercent = packageDuration.discount_percent || 0;

        // Apply package discount first to get the base discounted price
        let baseDiscountedPrice = originalPrice;
        if (packageDiscountPercent > 0) {
          baseDiscountedPrice =
            originalPrice * (1 - packageDiscountPercent / 100);
        }

        // For display purposes, we show the payment method discount percentage
        // but the actual discounted_price should be the base discounted price
        // The additional payment method discount will be applied in the frontend
        const discountedPrice = baseDiscountedPrice;

        pricingOptions.push({
          payment_method_id: discount.payment_method_id,
          payment_method_name: discount.paymentMethod?.name,
          original_price: originalPrice,
          discount_percent: paymentMethodDiscountPercent,
          discounted_price: parseFloat(discountedPrice.toFixed(2)),
          savings: parseFloat((originalPrice - discountedPrice).toFixed(2)),
          currency: currency,
        });
      }

      return {
        package_id: packageId,
        duration_id: durationId,
        duration_days: packageDuration.duration_days,
        base_price: parseFloat(packageDuration.price),
        currency: currency,
        pricing_options: pricingOptions,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        'Failed to retrieve package pricing',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number) {
    try {
      return await this.packagesService.findOne(id);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }
      throw new HttpException(
        'Failed to retrieve package',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
