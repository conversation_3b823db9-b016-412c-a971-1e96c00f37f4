import { IsArray, IsNumber, IsOptional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class ProductDiscountDto {
  @IsNumber()
  payment_method_id: number;

  @IsNumber()
  @IsOptional()
  discount_percent?: number;
}

export class ManageProductDiscountsDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProductDiscountDto)
  discounts: ProductDiscountDto[];
}
