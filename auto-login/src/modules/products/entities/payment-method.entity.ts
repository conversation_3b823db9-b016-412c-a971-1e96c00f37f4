import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { ProductDiscount } from './product-discount.entity';
import { PackageDiscount } from '../../packages/entities/package-discount.entity';

@Entity('payment_methods')
export class PaymentMethod {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  name: string;

  @Column('text', { nullable: true })
  description: string;

  @OneToMany(
    () => ProductDiscount,
    (productDiscount) => productDiscount.paymentMethod,
  )
  productDiscounts: ProductDiscount[];

  @OneToMany(
    () => PackageDiscount,
    (packageDiscount) => packageDiscount.paymentMethod,
  )
  packageDiscounts: PackageDiscount[];

  @CreateDateColumn({ name: 'created_at' })
  created_at: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updated_at: Date;
}
